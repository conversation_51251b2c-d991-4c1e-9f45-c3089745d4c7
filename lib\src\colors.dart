import 'package:flutter/material.dart';

class AppColors {
  // Primary Brand Colors
  static const Color primary = Color(0xFF3B82FBac); // Vibrant blue for primary actions
  static const Color primaryDark = Color(0xFF1E3A8A); // Darker blue for hover/focused states
  static const Color primaryLight = Color(0xFFEFF6FF); // Light blue for backgrounds
  static const Color secondary = Color(0xFFFF6B6B); // Warm coral for secondary actions
  static const Color accent = Color(0xFF22C55E); // Green for success states

  // Neutral Colors
  static const Color background = Color(0xFFF9FAFB); // Soft off-white for main background
  static const Color surface = Color(0xFFFFFFFF); // Pure white for cards and surfaces
  static const Color border = Color(0xFFE5E7EB); // Light gray for borders
  static const Color textPrimary = Color(0xFF1F2A44); // Dark navy for primary text
  static const Color textSecondary = Color(0xFF6B7280); // Muted gray for secondary text
  static const Color textDisabled = Color(0xFFA1A7B0); // Light gray for disabled text

  // Status Colors
  static const Color success = Color(0xFF22C55E); // Green for success
  static const Color error = Color(0xFFEF4444); // Red for errors
  static const Color warning = Color(0xFFF59E0B); // Amber for warnings
  static const Color info = Color(0xFF3B82F6); // Blue for informational elements

  // Shadows and Overlays
  static Color shadow = const Color(0xFF000000).withOpacity(0.05); // Subtle shadow
  static Color overlay = const Color(0xFF000000).withOpacity(0.4); // Modal overlay

  // Light Theme Colors
  static const LightThemeColors light = LightThemeColors();
}

class LightThemeColors {
  const LightThemeColors();

  // Background Colors
  Color get background => AppColors.background;
  Color get surface => AppColors.surface;
  Color get primaryLight => AppColors.primaryLight;

  // Text Colors
  Color get textPrimary => AppColors.textPrimary;
  Color get textSecondary => AppColors.textSecondary;
  Color get textDisabled => AppColors.textDisabled;
  Color get textOnPrimary => AppColors.surface;

  // Button Colors
  Color get buttonPrimary => AppColors.primary;
  Color get buttonSecondary => AppColors.secondary;
  Color get buttonDisabled => AppColors.textDisabled;

  // Border Colors
  Color get border => AppColors.border;
  Color get borderFocused => AppColors.primary;
  Color get borderError => AppColors.error;

  // Status Colors
  Color get success => AppColors.success;
  Color get error => AppColors.error;
  Color get warning => AppColors.warning;
  Color get info => AppColors.info;

  // Shadow and Overlay
  Color get shadow => AppColors.shadow;
  Color get overlay => AppColors.overlay;
}