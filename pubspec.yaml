name: admin
description: A new Flutter web application.

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  http: ^1.4.0
  provider: ^6.1.5
  shared_preferences: ^2.5.3
  intl: ^0.20.2
  flutter_inappwebview: ^6.1.5
  universal_html: ^2.2.4
  flutter_secure_storage: ^9.2.4
  flutter_pdfview: ^1.4.1+1
  path_provider: ^2.1.5
  webview_flutter: ^4.13.0  # Add this line
  file_picker: ^10.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  lints: ^6.0.0
  flutter_launcher_icons: ^0.14.1

dependency_overrides:
  meta: ^1.17.0
  leak_tracker: ^11.0.1
  leak_tracker_flutter_testing: ^3.0.10
  leak_tracker_testing: ^3.0.2
  material_color_utilities: ^0.13.0
  test_api: ^0.7.6
  vector_math: ^2.2.0
  vm_service: ^15.0.2

flutter:
  uses-material-design: true
  assets:
    - assets/A2020071.pdf
    - assets/A2022009.pdf
    - assets/icons/logo.jpg
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  web:
    generate: true
    image_path: "assets/icons/logo.jpg"
    background_color: "#ffffff"
    theme_color: "#ffffff"
  windows:
    generate: true
    image_path: "assets/icons/logo.jpg"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/logo.jpg"
  image_path: "assets/icons/logo.jpg"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/icons/logo.jpg"