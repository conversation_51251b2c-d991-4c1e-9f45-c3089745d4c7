import 'package:flutter/material.dart';
import '../colors.dart'; // Ensure this points to the file containing the AppColors class

class ThemeHelper {
  // Return LightThemeColors directly since we only use light mode
  static LightThemeColors getColors(BuildContext context) {
    return AppColors.light;
  }

  // Same as getColors since we don't need listener for theme changes
  static LightThemeColors getColorsWithListener(BuildContext context) {
    return AppColors.light;
  }

  static TextStyle getTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
      fontFamily: 'Cairo',
      height: 1.2,
    );
  }

  static TextStyle getSectionTitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: colors.textPrimary,
      fontFamily: 'Cairo',
      height: 1.3,
    );
  }

  static TextStyle getSubtitleStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 18,
      color: colors.textSecondary,
      fontFamily: 'Cairo',
      height: 1.5,
    );
  }

  static TextStyle getBodyStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.normal,
      color: colors.textPrimary,
    );
  }

  static TextStyle getButtonTextStyle(BuildContext context) {
    final colors = getColors(context);
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: colors.textOnPrimary,
      fontFamily: 'Cairo',
      letterSpacing: 0.5,
    );
  }

  static ButtonStyle getPrimaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonPrimary,
      foregroundColor: colors.textOnPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
    );
  }

  static ButtonStyle getSecondaryButtonStyle(BuildContext context) {
    final colors = getColors(context);
    return ElevatedButton.styleFrom(
      backgroundColor: colors.buttonSecondary,
      foregroundColor: colors.textPrimary,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: colors.border),
      ),
      elevation: 1,
    );
  }

  static InputDecoration getInputDecoration(
    BuildContext context, {
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    final colors = getColors(context);
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      labelStyle: TextStyle(color: colors.textSecondary),
      hintStyle: TextStyle(color: colors.textSecondary),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.border, width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.border, width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderFocused, width: 3.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderError, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colors.borderError, width: 3.0),
      ),
      filled: true,
      fillColor: colors.surface,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  static CardThemeData getCardTheme(BuildContext context) {
    final colors = getColors(context);
    return CardThemeData(
      color: colors.surface,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  static AppBarTheme getAppBarTheme(BuildContext context) {
    final colors = getColors(context);
    return AppBarTheme(
      backgroundColor: colors.background,
      foregroundColor: colors.textPrimary,
      elevation: 0,
      titleTextStyle: getSectionTitleStyle(context),
      iconTheme: IconThemeData(color: colors.textPrimary),
    );
  }

  static BottomNavigationBarThemeData getBottomNavTheme(BuildContext context) {
    final colors = getColors(context);
    return BottomNavigationBarThemeData(
      backgroundColor: colors.surface,
      selectedItemColor: AppColors.primary, // Replaced primaryOrange with primary
      unselectedItemColor: colors.textSecondary,
      type: BottomNavigationBarType.fixed,
    );
  }
}