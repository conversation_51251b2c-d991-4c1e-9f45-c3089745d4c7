import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../src/config/app_localizations.dart';
import '../src/config/theme_helper.dart';
import 'locale_provider.dart';
import 'login.dart';

class AppColors {
  static const Color primaryOrange = Color(0xFFFF5722);
  static const Color pureWhite = Color(0xFFFFFFFF);
  static const Color orangeLight = Color(0xFFFF8A65);
  static const Color orangePale = Color(0xFFFFEBE7);
  static const Color darkGray = Color(0xFF212121);
  static const Color mediumGray = Color(0xFF757575);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color borderGray = Color(0xFFE0E0E0);
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFD32F2F);
}

class AdminProfile extends StatefulWidget {
  const AdminProfile({super.key});

  @override
  State<AdminProfile> createState() => _AdminProfileState();
}

class _AdminProfileState extends State<AdminProfile> {
  final _storage = const FlutterSecureStorage();
  Map<String, dynamic>? _adminData;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchAdminProfile();
  }

  Future<void> _fetchAdminProfile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) throw Exception('No authentication token found');

      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/admin/profile'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final adminData = data['data']?['admin'] as Map<String, dynamic>? ?? data['data'] as Map<String, dynamic>?;
        if (adminData == null) throw Exception('Invalid profile data format');
        setState(() {
          _adminData = adminData;
          _isLoading = false;
        });
      } else {
        throw Exception('Failed to load profile: HTTP ${response.statusCode}');
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().contains('401') ? 'Session expired, please login again' : 'Error loading profile: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _logout(BuildContext context) async {
    bool isConfirmed = await _showLogoutConfirmation(context);
    if (!isConfirmed) return;

    setState(() => _isLoading = true);
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) throw Exception('No authentication token found');

      print('Logout request: POST https://api.rokhsati.yakoub-dev.h-s.cloud/api/auth/logout');
      final response = await http.post(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/auth/logout'),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('Logout response: ${response.statusCode} - ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 204) {
        await _storage.delete(key: 'auth_token');
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginApp()),
          (route) => false,
        );
      } else if (response.statusCode == 404) {
        print('Logout endpoint not found (404), clearing token locally as fallback');
        await _storage.delete(key: 'auth_token');
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginApp()),
          (route) => false,
        );
      } else {
        throw Exception('Logout failed: HTTP ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Logout error: $e';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_errorMessage ?? 'Logout failed')),
      );
    }
  }

  Future<bool> _showLogoutConfirmation(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.pureWhite,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          AppLocalizations.of(context)?.translate('confirm_logout') ?? 'Confirm Logout',
          style: const TextStyle(color: AppColors.darkGray, fontWeight: FontWeight.bold),
        ),
        content: Text(
          AppLocalizations.of(context)?.translate('are_you_sure_logout') ?? 'Are you sure you want to log out?',
          style: const TextStyle(color: AppColors.mediumGray),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              AppLocalizations.of(context)?.translate('cancel') ?? 'Cancel',
              style: const TextStyle(color: AppColors.mediumGray),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              AppLocalizations.of(context)?.translate('logout') ?? 'Logout',
              style: const TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: true);
    final isRtl = localeProvider.currentLanguage == 'ar';
    final localizations = AppLocalizations.of(context);

    if (localizations == null) {
      print('Warning: AppLocalizations is null in AdminProfile. Using default values.');
    }

    return Directionality(
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        backgroundColor: AppColors.lightGray,
        appBar: AppBar(
          backgroundColor: AppColors.pureWhite,
          elevation: 0,
          title: Text(
            localizations?.translate('profile') ?? 'Profile',
            style: const TextStyle(
              color: AppColors.darkGray,
              fontSize: 22,
              fontWeight: FontWeight.w600,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.primaryOrange),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 450),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            child: _isLoading
                ? const Center(child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation(AppColors.primaryOrange)))
                : _errorMessage != null
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline, size: 64, color: AppColors.error),
                          const SizedBox(height: 16),
                          Text(
                            _errorMessage!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: AppColors.darkGray, fontSize: 16),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryOrange,
                              foregroundColor: AppColors.pureWhite,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                            ),
                            onPressed: _fetchAdminProfile,
                            child: Text(
                              localizations?.translate('retry') ?? 'Retry',
                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                            ),
                          ),
                        ],
                      )
                    : SingleChildScrollView(
                        child: Card(
                          elevation: 4,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                          color: AppColors.pureWhite,
                          child: Padding(
                            padding: const EdgeInsets.all(24),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CircleAvatar(
                                  radius: 50,
                                  backgroundColor: AppColors.primaryOrange,
                                  child: _adminData?['avatar'] != null
                                      ? ClipOval(
                                          child: Image.network(
                                            _adminData!['avatar'] as String,
                                            width: 100,
                                            height: 100,
                                            fit: BoxFit.cover,
                                            loadingBuilder: (context, child, loadingProgress) {
                                              if (loadingProgress == null) return child;
                                              return const Center(child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation(AppColors.pureWhite)));
                                            },
                                            errorBuilder: (context, error, stackTrace) {
                                              return const Icon(Icons.person, size: 50, color: AppColors.pureWhite);
                                            },
                                          ),
                                        )
                                      : const Icon(Icons.person, size: 50, color: AppColors.pureWhite),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  _adminData?['name'] as String? ?? 'Admin Name',
                                  style: const TextStyle(
                                    fontSize: 26,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.darkGray,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _adminData?['email'] as String? ?? '<EMAIL>',
                                  style: const TextStyle(fontSize: 16, color: AppColors.mediumGray),
                                ),
                                const SizedBox(height: 24),
                                _buildCompactProfileRow(
                                  context,
                                  localizations: localizations,
                                  icon: Icons.work_outline,
                                  value: localizations?.translate('administrator') ?? 'Administrator',
                                ),
                                const Divider(height: 32, color: AppColors.borderGray),
                                if (_adminData?['phone'] != null)
                                  _buildCompactProfileRow(
                                    context,
                                    localizations: localizations,
                                    icon: Icons.phone,
                                    value: _adminData!['phone'] as String,
                                  ),
                                if (_adminData?['phone'] != null)
                                  const Divider(height: 32, color: AppColors.borderGray),
                                if (_adminData?['address'] != null)
                                  _buildCompactProfileRow(
                                    context,
                                    localizations: localizations,
                                    icon: Icons.location_on,
                                    value: _adminData!['address'] as String,
                                    maxLines: 2,
                                  ),
                                if (_adminData?['address'] != null)
                                  const SizedBox(height: 24),
                                SizedBox(
                                  width: double.infinity,
                                  child: OutlinedButton.icon(
                                    icon: const Icon(Icons.logout, size: 18, color: AppColors.error),
                                    label: Text(
                                      localizations?.translate('logout') ?? 'Logout',
                                      style: const TextStyle(fontSize: 16, color: AppColors.error),
                                    ),
                                    style: OutlinedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
                                      side: const BorderSide(color: AppColors.error),
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                      backgroundColor: AppColors.orangePale,
                                    ),
                                    onPressed: () => _logout(context),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactProfileRow(
    BuildContext context, {
    required IconData icon,
    required String value,
    int? maxLines,
    AppLocalizations? localizations,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 22, color: AppColors.primaryOrange),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 16, color: AppColors.darkGray),
            maxLines: maxLines,
            overflow: maxLines != null ? TextOverflow.ellipsis : null,
          ),
        ),
      ],
    );
  }
}