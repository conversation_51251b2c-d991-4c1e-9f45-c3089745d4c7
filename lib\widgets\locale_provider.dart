import 'package:flutter/material.dart';

class LocaleProvider with ChangeNotifier {
  Locale _locale = const Locale('en'); // Default locale

  Locale get locale => _locale;

  // Getter for current language code
  String get currentLanguage => _locale.languageCode;

  // List of supported locales
  static final List<Locale> _supportedLocales = const [
    Locale('en'),
    Locale('fr'),
    Locale('ar'),
  ];

  List<Locale> get supportedLocales => _supportedLocales;

  void setLocale(Locale locale) {
    if (!_supportedLocales.map((l) => l.languageCode).contains(locale.languageCode)) return;
    _locale = locale;
    notifyListeners();
  }
}