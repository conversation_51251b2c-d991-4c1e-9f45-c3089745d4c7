// Exemples d'utilisation des nouvelles traductions
// Ce fichier montre comment remplacer les chaînes hardcodées par les nouvelles clés de traduction

import 'package:flutter/material.dart';
import '../src/config/app_localizations.dart';

class TranslationExamples {
  
  // EXEMPLE 1: Messages d'erreur dans dashboard.dart
  void _showErrorWithTranslation(BuildContext context, String errorKey, String fallback) {
    final localizations = AppLocalizations.of(context);
    final message = localizations?.translate(errorKey) ?? fallback;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  // AVANT (hardcodé):
  void _updateRequestStatusOld(String requestId) {
    if (requestId.isEmpty) {
      // _showErrorSnackBar('Invalid request ID');
      return;
    }
  }

  // APRÈS (avec traduction):
  void _updateRequestStatusNew(BuildContext context, String requestId) {
    if (requestId.isEmpty) {
      _showErrorWithTranslation(context, 'invalid_request_id', 'Invalid request ID');
      return;
    }
  }

  // EXEMPLE 2: Dialogues de confirmation
  Future<bool> _showConfirmationDialog(
    BuildContext context,
    String titleKey,
    String messageKey,
    String titleFallback,
    String messageFallback,
  ) async {
    final localizations = AppLocalizations.of(context);
    
    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            localizations?.translate(titleKey) ?? titleFallback,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            localizations?.translate(messageKey) ?? messageFallback,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                localizations?.translate('cancel') ?? 'Cancel',
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                localizations?.translate('confirm') ?? 'Confirm',
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  // EXEMPLE 3: Utilisation pour approuver une demande
  void _approveRequest(BuildContext context, String requestId) {
    _showConfirmationDialog(
      context,
      'approve_request_title',
      'approve_request_message',
      'Approve Request',
      'Are you sure you want to approve this request? You will need to upload a permit PDF.',
    ).then((confirmed) {
      if (confirmed) {
        // Logique d'approbation
        _updateRequestStatus(context, requestId, 'approved');
      }
    });
  }

  // EXEMPLE 4: Affichage des statuts traduits
  Widget _buildStatusChip(BuildContext context, String? status) {
    final localizations = AppLocalizations.of(context);
    
    Color backgroundColor;
    Color textColor = Colors.white;
    String displayStatus;
    
    switch (status?.toLowerCase()) {
      case 'approved':
        backgroundColor = Colors.green;
        displayStatus = localizations?.translate('status_approved') ?? 'APPROVED';
        break;
      case 'rejected':
        backgroundColor = Colors.red;
        displayStatus = localizations?.translate('status_rejected') ?? 'REJECTED';
        break;
      case 'pending':
        backgroundColor = Colors.orange;
        displayStatus = localizations?.translate('status_pending') ?? 'PENDING';
        break;
      default:
        backgroundColor = Colors.grey;
        displayStatus = localizations?.translate('unknown_status') ?? 'UNKNOWN';
    }

    return Chip(
      label: Text(
        displayStatus,
        style: TextStyle(color: textColor, fontWeight: FontWeight.bold),
      ),
      backgroundColor: backgroundColor,
    );
  }

  // EXEMPLE 5: Sélecteur de langue amélioré
  Widget _buildLanguageSelector(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return DropdownButton<String>(
      items: [
        DropdownMenuItem(
          value: 'en',
          child: Text(localizations?.translate('language_english') ?? 'English'),
        ),
        DropdownMenuItem(
          value: 'fr',
          child: Text(localizations?.translate('language_french') ?? 'Français'),
        ),
        DropdownMenuItem(
          value: 'ar',
          child: Text(localizations?.translate('language_arabic') ?? 'العربية'),
        ),
      ],
      onChanged: (String? newValue) {
        // Logique de changement de langue
      },
    );
  }

  // EXEMPLE 6: Messages de succès
  void _showSuccessMessage(BuildContext context, String successKey, String fallback) {
    final localizations = AppLocalizations.of(context);
    final message = localizations?.translate(successKey) ?? fallback;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // EXEMPLE 7: Gestion des utilisateurs inconnus
  String _getUserDisplayName(BuildContext context, Map<String, dynamic>? userData) {
    final localizations = AppLocalizations.of(context);
    
    if (userData == null || userData['name'] == null) {
      return localizations?.translate('unknown_user') ?? 'Unknown User';
    }
    
    return userData['name'] as String;
  }

  // EXEMPLE 8: Formatage des dates avec fallback traduit
  String _formatDateWithFallback(BuildContext context, String? dateString) {
    final localizations = AppLocalizations.of(context);
    
    if (dateString == null) {
      return localizations?.translate('date_not_available') ?? 'N/A';
    }
    
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return localizations?.translate('date_not_available') ?? 'N/A';
    }
  }

  // EXEMPLE 9: Messages pour les listes vides
  Widget _buildEmptyState(BuildContext context, String emptyMessageKey, String fallback, IconData icon) {
    final localizations = AppLocalizations.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            localizations?.translate(emptyMessageKey) ?? fallback,
            style: const TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  // EXEMPLE 10: Bouton de retry avec traduction
  Widget _buildRetryButton(BuildContext context, VoidCallback onRetry) {
    final localizations = AppLocalizations.of(context);
    
    return ElevatedButton(
      onPressed: onRetry,
      child: Text(
        localizations?.translate('retry') ?? 'Retry',
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      ),
    );
  }

  // Méthodes utilitaires pour les exemples
  void _updateRequestStatus(BuildContext context, String requestId, String status) {
    // Implémentation de mise à jour du statut
  }
}

/*
RÉSUMÉ DES CHANGEMENTS À APPLIQUER:

1. Remplacer tous les messages d'erreur hardcodés:
   - 'Invalid request ID' → 'invalid_request_id'
   - 'No PDF file selected' → 'no_pdf_selected'
   - 'Request status updated successfully' → 'request_updated_success'

2. Utiliser les nouvelles clés pour les dialogues:
   - 'Approve Request' → 'approve_request_title'
   - 'Reject Request' → 'reject_request_title'
   - 'Confirm Logout' → 'confirm_logout'

3. Traduire les statuts:
   - 'APPROVED' → 'status_approved'
   - 'REJECTED' → 'status_rejected'
   - 'PENDING' → 'status_pending'

4. Utiliser les fallbacks traduits:
   - 'Unknown User' → 'unknown_user'
   - 'N/A' → 'date_not_available'
   - 'No reason provided' → 'no_reason_provided'

5. Améliorer le sélecteur de langue:
   - 'English' → 'language_english'
   - 'Français' → 'language_french'
   - 'العربية' → 'language_arabic'
*/
